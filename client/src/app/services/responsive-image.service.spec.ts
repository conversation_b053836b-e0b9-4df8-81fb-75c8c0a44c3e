import { TestBed } from '@angular/core/testing';
import { ResponsiveImageService } from './responsive-image.service';

describe('ResponsiveImageService', () => {
  let service: ResponsiveImageService;

  beforeEach(() => {
    TestBed.configureTestingModule({});
    service = TestBed.inject(ResponsiveImageService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should generate responsive image attributes for priority images', () => {
    const config = {
      src: 'https://example.com/image.jpg',
      alt: 'Test image',
      priority: true
    };

    const attributes = service.getResponsiveImageAttributes(config);

    expect(attributes.ngSrc).toBe(config.src);
    expect(attributes.alt).toBe(config.alt);
    expect(attributes.priority).toBe(true);
    expect(attributes.loading).toBeUndefined(); // Priority images should not have loading attribute
    expect(attributes.fetchpriority).toBe('high');
    expect(attributes.width).toBe(930);
    expect(attributes.height).toBe(456);
    expect(attributes.sizes).toContain('(max-width: 350px) 320px');
  });

  it('should generate responsive image attributes for non-priority images', () => {
    const config = {
      src: 'https://example.com/image.jpg',
      alt: 'Test image',
      priority: false
    };

    const attributes = service.getResponsiveImageAttributes(config);

    expect(attributes.ngSrc).toBe(config.src);
    expect(attributes.alt).toBe(config.alt);
    expect(attributes.priority).toBe(false);
    expect(attributes.loading).toBe('lazy');
    expect(attributes.fetchpriority).toBe('auto');
  });

  it('should detect priority correctly', () => {
    expect(service.shouldHavePriority('main-content')).toBe(true);
    expect(service.shouldHavePriority('similar-content')).toBe(false);
    expect(service.shouldHavePriority('thumbnail')).toBe(false);
  });

  it('should generate proper alt text', () => {
    const content = {
      title: 'Test Article',
      description: 'Test description'
    };

    const altText = service.generateAltText(content);
    expect(altText).toBe('Test Article - статья на Advayta.org');
  });

  it('should generate fallback alt text', () => {
    const content = {};
    const altText = service.generateAltText(content, 'Fallback text');
    expect(altText).toBe('Fallback text');
  });
});
