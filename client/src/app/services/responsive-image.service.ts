import { Injectable, inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';

export interface ResponsiveImageConfig {
  src: string;
  alt: string;
  priority?: boolean;
  aspectRatio?: string;
  objectFit?: 'cover' | 'contain' | 'fill' | 'scale-down' | 'none';
}

export interface ResponsiveImageAttributes {
  ngSrc: string;
  width: number;
  height: number;
  sizes: string;
  alt: string;
  priority?: boolean;
  loading?: 'lazy' | 'eager';
  fetchpriority?: 'high' | 'low' | 'auto';
  style?: { [key: string]: string };
}

@Injectable({
  providedIn: 'root'
})
export class ResponsiveImageService {
  private platformId = inject(PLATFORM_ID);

  // Breakpoints based on your CSS
  private readonly breakpoints = {
    mobile: 350,
    smallMobile: 450,
    tablet: 600,
    mediumTablet: 768,
    desktop: 1008,
    largeDesktop: 1200
  };

  // Container sizes at different breakpoints (based on your CSS)
  private readonly containerSizes = {
    mobile: { width: 320, height: 142 },      // max-width: 350px
    smallMobile: { width: 400, height: 162 }, // max-width: 450px  
    tablet: { width: 580, height: 226 },      // max-width: 600px
    mediumTablet: { width: 580, height: 284 }, // max-width: 768px
    desktop: { width: 930, height: 456 },     // max-width: 1008px
    largeDesktop: { width: 930, height: 456 } // > 1008px
  };

  /**
   * Get responsive image attributes for NgOptimizedImage
   */
  getResponsiveImageAttributes(config: ResponsiveImageConfig): ResponsiveImageAttributes {
    const sizes = this.generateSizesAttribute();
    const { width, height } = this.getOptimalDimensions();

    const attributes: ResponsiveImageAttributes = {
      ngSrc: config.src,
      width,
      height,
      sizes,
      alt: config.alt,
      priority: config.priority,
      fetchpriority: config.priority ? 'high' : 'auto',
      style: {
        'object-fit': config.objectFit || 'cover',
        'width': '100%',
        'height': '100%',
        ...(config.aspectRatio && { 'aspect-ratio': config.aspectRatio })
      }
    };

    // Only add loading attribute for non-priority images
    if (!config.priority) {
      attributes.loading = 'lazy';
    }

    return attributes;
  }

  /**
   * Generate sizes attribute based on breakpoints using viewport units
   * Based on actual container sizes from CSS
   */
  private generateSizesAttribute(): string {
    return [
      `(max-width: ${this.breakpoints.mobile}px) calc(100vw - 40px)`, // Mobile with padding
      `(max-width: ${this.breakpoints.smallMobile}px) calc(100vw - 40px)`, // Small mobile with padding
      `(max-width: ${this.breakpoints.tablet}px) calc(100vw - 60px)`, // Tablet with padding
      `(max-width: ${this.breakpoints.mediumTablet}px) calc(100vw - 60px)`, // Medium tablet
      `(max-width: ${this.breakpoints.desktop}px) min(930px, calc(100vw - 80px))`, // Desktop max width
      `min(930px, calc(100vw - 80px))` // Large desktop
    ].join(', ');
  }

  /**
   * Get viewport-specific image dimensions for better performance
   */
  getViewportSpecificDimensions(): { width: number; height: number } {
    const viewportSize = this.getCurrentViewportSize();
    return viewportSize || this.containerSizes.largeDesktop;
  }

  /**
   * Get optimal dimensions for the largest viewport
   */
  private getOptimalDimensions(): { width: number; height: number } {
    return {
      width: this.containerSizes.largeDesktop.width,
      height: this.containerSizes.largeDesktop.height
    };
  }

  /**
   * Get current viewport size (client-side only)
   */
  getCurrentViewportSize(): { width: number; height: number } | null {
    if (!isPlatformBrowser(this.platformId)) {
      return null;
    }

    const width = window.innerWidth;
    
    if (width <= this.breakpoints.mobile) {
      return this.containerSizes.mobile;
    } else if (width <= this.breakpoints.smallMobile) {
      return this.containerSizes.smallMobile;
    } else if (width <= this.breakpoints.tablet) {
      return this.containerSizes.tablet;
    } else if (width <= this.breakpoints.mediumTablet) {
      return this.containerSizes.mediumTablet;
    } else if (width <= this.breakpoints.desktop) {
      return this.containerSizes.desktop;
    } else {
      return this.containerSizes.largeDesktop;
    }
  }

  /**
   * Check if image should have priority loading
   */
  shouldHavePriority(imageType: 'main-content' | 'similar-content' | 'thumbnail'): boolean {
    return imageType === 'main-content';
  }

  /**
   * Generate optimized alt text for SEO
   */
  generateAltText(content: any, fallback: string = ''): string {
    if (content?.title) {
      return `${content.title} - статья на Advayta.org`;
    }
    if (content?.description) {
      return content.description.substring(0, 100) + '...';
    }
    return fallback || 'Изображение статьи';
  }

  /**
   * Get image URL with potential optimization parameters
   * (Ready for future server-side image processing)
   */
  getOptimizedImageUrl(baseUrl: string, _width?: number, _height?: number, _quality?: number): string {
    // For now, return the original URL
    // In the future, you can add query parameters for server-side resizing:
    // return `${baseUrl}?w=${_width}&h=${_height}&q=${_quality || 80}`;
    return baseUrl;
  }

  /**
   * Preload critical images
   */
  preloadCriticalImage(src: string): void {
    if (!isPlatformBrowser(this.platformId)) return;

    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = src;
    link.fetchPriority = 'high';
    document.head.appendChild(link);
  }
}
